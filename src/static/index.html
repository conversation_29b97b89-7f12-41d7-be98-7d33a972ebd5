<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capsy - AI Video Caption Studio</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Source+Sans+Pro:wght@300;400;600;700&family=Oswald:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&family=Ubuntu:wght@300;400;500;700&family=Nunito:wght@300;400;600;700&family=Playfair+Display:wght@400;500;600;700&family=Merriweather:wght@300;400;700&family=Bebas+Neue&family=Anton&family=Fjalla+One&family=Quicksand:wght@300;400;500;600;700&family=Work+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-brand">
                <div class="logo">
                    <i class="fas fa-video"></i>
                    <span class="logo-text">Capsy</span>
                </div>
                <span class="tagline">AI Video Caption Studio</span>
            </div>
            <div class="header-actions">
                <button class="btn btn-outline" id="saveProjectBtn" disabled>
                    <i class="fas fa-save"></i>
                    <span>Save Project</span>
                </button>
                <button class="btn btn-primary" id="exportBtn" disabled>
                    <i class="fas fa-download"></i>
                    <span>Export Video</span>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section" id="uploadSection">
                <div class="upload-container">
                    <div class="upload-hero">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h2 class="upload-title">Upload Your Video</h2>
                            <p class="upload-subtitle">Drag and drop your video file here or click to browse</p>
                            <input type="file" id="videoInput" accept="video/*" style="display: none;">
                            <button class="btn btn-primary btn-large" id="browseBtn">
                                <i class="fas fa-folder-open"></i>
                                <span>Choose Video File</span>
                            </button>
                            <div class="file-info" id="fileInfo"></div>

                            <!-- Progress Indicator - Inside Upload Area -->
                            <div class="progress-container" id="progressContainer" style="display: none;">
                                <div class="progress-wrapper">
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="progressFill"></div>
                                    </div>
                                    <div class="progress-info">
                                        <span class="progress-text" id="progressText">Processing...</span>
                                        <span class="progress-percentage" id="progressPercentage">0%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="upload-limits">
                                <span><i class="fas fa-info-circle"></i> Max file size: 100MB</span>
                                <span><i class="fas fa-file-video"></i> Supported: MP4, AVI, MOV, WebM</span>
                            </div>
                        </div>
                    </div>

                    <!-- Features Grid -->
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h3>AI Transcription</h3>
                            <p>Powered by OpenAI Whisper for accurate speech-to-text with precise word-level timing</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h3>Smart Editing</h3>
                            <p>Intuitive timeline editor with drag-and-drop caption positioning and real-time preview</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <h3>Visual Effects</h3>
                            <p>Professional video filters and customizable caption styling for stunning results</p>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Editor Section -->
            <section class="editor-section" id="editorSection" style="display: none;">
                <div class="editor-layout">
                    <!-- Video Player Area -->
                    <div class="video-area">
                        <div class="video-container">
                            <video id="videoPlayer" controls>
                                Your browser does not support the video tag.
                            </video>
                            <div class="caption-overlay" id="captionOverlay"></div>

                            <!-- Hidden video controls for JavaScript access -->
                            <div style="display: none;">
                                <button id="playPauseBtn">Play/Pause</button>
                                <span id="currentTime">0:00</span>
                                <span id="totalTime">0:00</span>
                                <input type="range" id="seekBar" min="0" max="100" value="0">
                                <input type="range" id="volumeBar" min="0" max="100" value="100">
                                <button id="muteBtn">Mute</button>
                            </div>
                        </div>

                        <!-- Timeline Section -->
                        <div class="timeline-section">
                            <div class="timeline-header">
                                <h3><i class="fas fa-timeline"></i> Timeline Editor</h3>
                                <div class="timeline-controls">
                                    <div class="word-grouping-toggle">
                                        <label class="toggle-switch" title="Group words by timing">
                                            <input type="checkbox" id="wordGroupingToggle">
                                            <span class="toggle-slider"></span>
                                        </label>
                                        <span class="toggle-label">Group Words</span>
                                    </div>
                                    <button class="btn btn-sm" id="zoomInBtn" title="Zoom In">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="btn btn-sm" id="zoomOutBtn" title="Zoom Out">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="btn btn-sm" id="autoSyncBtn" title="Auto Sync">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="timeline-container" id="timelineContainer">
                                <div class="timeline-ruler" id="timelineRuler"></div>
                                <div class="timeline-track" id="timelineTrack"></div>
                                <div class="timeline-playhead" id="playhead"></div>
                            </div>
                        </div>
                    </div>
                    <!-- Control Panels -->
                    <div class="control-panels">
                        <!-- Caption Style Panel -->
                        <div class="control-panel" id="captionPanel">
                            <div class="panel-header" data-panel="caption">
                                <h3><i class="fas fa-font"></i> Caption Style</h3>
                                <button class="panel-toggle">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <div class="panel-content">
                                <div class="control-grid">
                                    <div class="control-group">
                                        <label for="fontFamily">Font Family</label>
                                        <select id="fontFamily" class="form-control">
                                            <option value="Inter">Inter</option>
                                            <option value="Arial">Arial</option>
                                            <option value="Helvetica">Helvetica</option>
                                            <option value="Georgia">Georgia</option>
                                            <option value="Times New Roman">Times New Roman</option>
                                            <option value="Courier New">Courier New</option>
                                            <option value="Impact">Impact</option>
                                            <option value="Roboto">Roboto</option>
                                            <option value="Open Sans">Open Sans</option>
                                            <option value="Lato">Lato</option>
                                            <option value="Montserrat">Montserrat</option>
                                            <option value="Poppins">Poppins</option>
                                            <option value="Source Sans Pro">Source Sans Pro</option>
                                            <option value="Oswald">Oswald</option>
                                            <option value="Raleway">Raleway</option>
                                            <option value="Ubuntu">Ubuntu</option>
                                            <option value="Nunito">Nunito</option>
                                            <option value="Playfair Display">Playfair Display</option>
                                            <option value="Merriweather">Merriweather</option>
                                            <option value="Bebas Neue">Bebas Neue</option>
                                            <option value="Anton">Anton</option>
                                            <option value="Fjalla One">Fjalla One</option>
                                            <option value="Quicksand">Quicksand</option>
                                            <option value="Work Sans">Work Sans</option>
                                        </select>
                                    </div>

                                    <div class="control-group">
                                        <label for="fontSize">Font Size</label>
                                        <div class="range-control">
                                            <input type="range" id="fontSize" class="form-range" min="12" max="72" value="24">
                                            <span id="fontSizeValue" class="range-value">24px</span>
                                        </div>
                                    </div>

                                    <div class="control-group">
                                        <div class="checkbox-input">
                                            <label>
                                                <input type="checkbox" id="textUppercase">
                                                <span class="checkmark"></span>
                                                Full Capital Letters
                                            </label>
                                        </div>
                                    </div>

                                    <div class="control-group">
                                        <label>Colors</label>
                                        <div class="color-controls">
                                            <div class="color-input">
                                                <label for="fontColor">Text</label>
                                                <input type="color" id="fontColor" value="#ffffff">
                                            </div>
                                            <div class="color-input">
                                                <label for="backgroundColor">Background</label>
                                                <input type="color" id="backgroundColor" value="#000000">
                                            </div>
                                            <div class="checkbox-input">
                                                <label>
                                                    <input type="checkbox" id="showBackground">
                                                    <span class="checkmark"></span>
                                                    Show BG
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="control-group">
                                        <label>Position</label>
                                        <div class="position-controls">
                                            <div class="position-input">
                                                <label for="positionX">Horizontal</label>
                                                <div class="range-control">
                                                    <input type="range" id="positionX" class="form-range" min="0" max="100" value="50">
                                                    <span id="positionXValue" class="range-value">50%</span>
                                                </div>
                                            </div>
                                            <div class="position-input">
                                                <label for="positionY">Vertical</label>
                                                <div class="range-control">
                                                    <input type="range" id="positionY" class="form-range" min="0" max="100" value="50">
                                                    <span id="positionYValue" class="range-value">50%</span>
                                                </div>
                                            </div>
                                            <div class="position-input">
                                                <label for="captionWidth">Width</label>
                                                <div class="range-control">
                                                    <input type="range" id="captionWidth" class="form-range" min="20" max="100" value="50">
                                                    <span id="captionWidthValue" class="range-value">50%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Video Effects Panel -->
                        <div class="control-panel" id="effectsPanel">
                            <div class="panel-header" data-panel="effects">
                                <h3><i class="fas fa-magic"></i> Video Effects</h3>
                                <button class="panel-toggle">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <div class="panel-content">
                                <div class="control-grid">
                                    <div class="control-group">
                                        <label for="videoEffect">Effect Type</label>
                                        <select id="videoEffect" class="form-control">
                                            <option value="none">None</option>
                                            <option value="grayscale">Grayscale</option>
                                            <option value="sepia">Sepia</option>
                                            <option value="blur">Blur</option>
                                            <option value="brightness">Brightness</option>
                                            <option value="contrast">Contrast</option>
                                            <option value="saturate">Saturate</option>
                                        </select>
                                    </div>

                                    <div class="control-group">
                                        <label for="effectIntensity">Intensity</label>
                                        <div class="range-control">
                                            <input type="range" id="effectIntensity" class="form-range" min="0" max="200" value="100">
                                            <span id="effectIntensityValue" class="range-value">100%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Transcript Panel -->
                        <div class="control-panel" id="transcriptPanel">
                            <div class="panel-header" data-panel="transcript">
                                <h3><i class="fas fa-file-alt"></i> Transcript</h3>
                                <button class="panel-toggle">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <div class="panel-content">
                                <div class="transcript-loading" id="transcriptLoading" style="display: none;">
                                    <p>Loading transcript...</p>
                                </div>
                                <div class="transcript-content" id="transcriptContent">
                                    <p class="transcript-placeholder">Upload a video to see the transcript here...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Status Bar -->
        <div class="status-bar" id="statusBar">
            <div class="status-content">
                <div class="status-indicator">
                    <i class="fas fa-circle" id="statusIcon"></i>
                    <span id="statusText">Ready to upload video</span>
                </div>
                <div class="status-info" id="statusInfo"></div>
            </div>
        </div>
    </div>

    <!-- Scripts - Load dependencies first -->
    <script>
        // Debug script to check DOM elements
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded, checking elements...');
            console.log('uploadArea:', document.getElementById('uploadArea'));
            console.log('videoPlayer:', document.getElementById('videoPlayer'));
            console.log('statusText:', document.getElementById('statusText'));
            console.log('VideoPlayer class:', typeof VideoPlayer);
        });
    </script>
    <script src="js/video.js"></script>
    <script src="js/timeline.js"></script>
    <script src="js/effects.js"></script>
    <script src="js/main.js"></script>
</body>
</html>